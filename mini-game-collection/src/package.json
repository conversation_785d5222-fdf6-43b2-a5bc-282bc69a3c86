{"id": "hello-uniapp", "name": "hello-uniapp", "displayName": "hello-uniapp 示例工程", "version": "3.4.9", "description": "uni-app 框架示例，一套代码，同时发行到iOS、Android、H5、小程序等多个平台，请使用手机扫码快速体验 uni-app 的强大功能", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": "https://github.com/dcloudio/hello-uniapp.git", "keywords": ["hello-uniapp", "uni-app", "uni-ui", "示例工程"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/dcloudio/hello-uniapp/issues"}, "homepage": "https://github.com/dcloudio/hello-uniapp#readme", "dependencies": {}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "uniapp-template-project", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "x"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "√", "android": "√", "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "√", "baidu": "√", "kuaishou": "√", "jd": "√", "harmony": "√", "qq": "√", "lark": "√"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}, "uni-app": {"scripts": {"mp-dingtalk": {"title": "钉钉小程序", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-DINGTALK": true}}, "hello-uniapp-demo": {"title": "hello-uniapp 演示网站", "env": {"UNI_PLATFORM": "h5"}, "define": {"H5-DEMO": true}}}}, "engines": {"HBuilderX": "^3.1.0", "uni-app": "^4.03", "uni-app-x": ""}}