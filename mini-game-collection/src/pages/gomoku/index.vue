<template>
  <view class="gomoku-page">
    <view class="game-header">
      <text class="game-title">五子棋</text>
      <button class="back-btn" @click="navigateBack">返回</button>
    </view>
    <view class="game-container">
      <canvas canvas-id="gameCanvas" class="game-canvas" @touchstart="handleTouch"></canvas>
      <view class="game-info">
        <view class="status-panel">
          <text class="info-label">当前玩家：</text>
          <text class="player-color">{{ currentPlayer === 'black' ? '黑棋' : '白棋' }}</text>
        </view>
        <view class="control-buttons">
          <button class="control-btn" @click="restartGame">重新开始</button>
          <button class="control-btn" @click="togglePause">{{ isPaused ? '继续' : '暂停' }}</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentPlayer: 'black', // 'black' or 'white'
      isPaused: false,
      boardSize: 15, // 15x15棋盘
      cellSize: 40,
      board: [], // 存储棋盘状态
      canvasWidth: 600,
      canvasHeight: 600
    };
  },
  onLoad() {
    this.initGame();
  },
  methods: {
    initGame() {
      // 初始化棋盘
      this.board = Array(this.boardSize).fill().map(() => Array(this.boardSize).fill(null));
      this.currentPlayer = 'black';
      this.drawBoard();
    },
    drawBoard() {
      // 绘制棋盘网格和棋子
      const ctx = uni.createCanvasContext('gameCanvas', this);
      // 绘制逻辑将在这里实现
    },
    handleTouch(e) {
      // 处理落子逻辑
      if (this.isPaused) return;
      // 计算点击位置对应的棋盘坐标
    },
    checkWin(x, y) {
      // 判断胜负逻辑
      return false;
    },
    restartGame() {
      this.initGame();
    },
    togglePause() {
      this.isPaused = !this.isPaused;
    },
    navigateBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style scoped>
.gomoku-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}
.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.game-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.back-btn {
  background-color: #99ccff;
  color: white;
  border-radius: 50rpx;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
}
.game-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.game-canvas {
  width: 600rpx;
  height: 600rpx;
  background-color: #ffe4b5;
  border-radius: 10rpx;
  box-shadow: 0 0 10rpx rgba(0,0,0,0.1);
}
.game-info {
  margin-top: 20rpx;
  width: 600rpx;
}
.status-panel {
  text-align: center;
  margin-bottom: 20rpx;
  font-size: 32rpx;
}
.player-color {
  color: #000;
  font-weight: bold;
}
.control-buttons {
  display: flex;
  justify-content: space-around;
}
.control-btn {
  background-color: #cce5ff;
  color: #333;
  border-radius: 10rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
}
</style>