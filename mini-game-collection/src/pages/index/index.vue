<template>
  <view class="home-page">
    <view class="title-container">
      <image class="title-icon" src="/static/game-icon.svg" mode="aspectFit"></image>
      <text class="main-title">小游戏集合</text>
      <text class="sub-title">选择你想玩的游戏</text>
    </view>
    <view class="games-grid">
      <view class="game-card" @click="navigateToGame('tetris')">
        <image class="game-icon" src="/static/tetris-icon.png" mode="aspectFit"></image>
        <text class="game-name">俄罗斯方块</text>
      </view>
      <view class="game-card" @click="navigateToGame('gomoku')">
        <image class="game-icon" src="/static/gomoku-icon.png" mode="aspectFit"></image>
        <text class="game-name">五子棋</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    navigateToGame(game) {
      if (game === 'tetris') {
        uni.switchTab({ url: '/pages/tetris/index' });
      } else if (game === 'gomoku') {
        uni.switchTab({ url: '/pages/gomoku/index' });
      }
    }
  }
};
</script>

<style scoped>
.home-page {
  background-color: #f0f8ff;
  min-height: 100vh;
  padding: 40rpx;
}
.title-container {
  text-align: center;
  margin: 40rpx 0 60rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #fff0f5 0%, #ffe4e1 100%);
  border-radius: 20rpx;
  box-shadow: 0 5rpx 15rpx rgba(255,102,153,0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.main-title {
  font-size: 52rpx;
  font-weight: bold;
  color: #ff6699;
  margin: 20rpx 0 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}
.title-icon {
  width: 80rpx;
  height: 80rpx;
  margin-top: 10rpx;
}
.sub-title {
  font-size: 32rpx;
  color: #666;
}
.games-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40rpx;
}
.game-card {
  background-color: white;
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 20rpx rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}
.game-card:hover {
  transform: translateY(-10rpx);
}
.game-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}
.game-name {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}
</style>