<template>
  <view class="tetris-page">
    <view class="game-header">
      <h1 class="game-title">俄罗斯方块</h1>
      <button class="back-btn" @click="navigateBack">返回</button>
    </view>
    <view class="game-info">
      <div class="score">分数: <span id="score">{{ score }}</span></div>
      <button id="start-btn" class="game-btn" @click="startGame">开始游戏</button>
    </view>
    <view class="game-container">
      <canvas canvas-id="gameCanvas" class="game-canvas" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd"></canvas>
      <!-- 添加控制按钮区域 -->
      <div class="controls">
        <div class="control-row">
          <button id="rotate-btn" class="control-btn" @click="rotate">⟳ 旋转</button>
        </div>
        <div class="control-row">
          <button id="left-btn" class="control-btn" @click="moveLeft">← 左移</button>
          <button id="down-btn" class="control-btn" @click="moveDown">↓ 下移</button>
          <button id="right-btn" class="control-btn" @click="moveRight">→ 右移</button>
        </div>
        <button id="pause-btn" class="game-btn" @click="togglePause">{{ isPaused ? '继续游戏' : '暂停游戏' }}</button>
      </div>
        </view>
      </view>
</template>

<script>
export default {
  data() {
    return {
      score: 0,
      isPaused: false,
      canvasWidth: 300,
      canvasHeight: 600,
      squareSize: 30,
      gameInterval: null,
      ctx: null,
      currentTetromino: null,
      // 游戏状态变量将在onLoad中初始化
    };
  },
  onLoad() {
    this.initGame();
  },
  onUnload() {
    this.stopGame();
  },
  methods: {
    initGame() {
      // 初始化游戏数据和画布
      this.ctx = uni.createCanvasContext('gameCanvas', this);
      // 设置画布背景
      this.ctx.fillStyle = '#f0f8ff';
      this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      // 绘制网格
      this.drawGrid();
      // 初始化方块
      this.currentTetromino = this.createTetromino();
      this.drawTetromino();
    },
    drawGrid() {
      // 绘制网格线
      this.ctx.strokeStyle = '#e0e0e0';
      this.ctx.lineWidth = 1;
      for (let x = 0; x <= this.canvasWidth; x += this.squareSize) {
        this.ctx.beginPath();
        this.ctx.moveTo(x, 0);
        this.ctx.lineTo(x, this.canvasHeight);
        this.ctx.stroke();
      }
      for (let y = 0; y <= this.canvasHeight; y += this.squareSize) {
        this.ctx.beginPath();
        this.ctx.moveTo(0, y);
        this.ctx.lineTo(this.canvasWidth, y);
        this.ctx.stroke();
      }
    },
    createTetromino() {
      // 创建随机方块
      const tetrominoes = [
        [[1, 1, 1, 1]], // I
        [[1, 1], [1, 1]], // O
        [[1, 1, 1], [0, 1, 0]], // T
        [[1, 1, 1], [1, 0, 0]], // L
        [[1, 1, 1], [0, 0, 1]], // J
        [[0, 1, 1], [1, 1, 0]], // S
        [[1, 1, 0], [0, 1, 1]]  // Z
      ];
      const type = Math.floor(Math.random() * tetrominoes.length);
      return {
        shape: tetrominoes[type],
        x: Math.floor((this.canvasWidth / this.squareSize - tetrominoes[type][0].length) / 2),
        y: 0,
        color: this.getRandomColor()
      };
    },
    getRandomColor() {
      // 获取随机颜色
      const colors = ['#FF69B4', '#FF6347', '#FFA500', '#FFFF00', '#7CFC00', '#00FFFF', '#1E90FF'];
      return colors[Math.floor(Math.random() * colors.length)];
    },
    drawTetromino() {
      // 绘制方块
      this.currentTetromino.shape.forEach((row, y) => {
        row.forEach((cell, x) => {
          if (cell) {
            this.ctx.fillStyle = this.currentTetromino.color;
            this.ctx.fillRect(
              (this.currentTetromino.x + x) * this.squareSize,
              (this.currentTetromino.y + y) * this.squareSize,
              this.squareSize - 1,
              this.squareSize - 1
            );
          }
        });
      });
      this.ctx.draw();
    },
    startGame() {
      if (!this.gameInterval) {
        this.score = 0;
        this.isPaused = false;
        this.initGame();
        this.gameInterval = setInterval(() => {
          this.moveDown();
        }, 1000);
      }
    },
    stopGame() {
      // 停止游戏逻辑
      if (this.gameInterval) {
        clearInterval(this.gameInterval);
        this.gameInterval = null;
      }
    },
    togglePause() {
      this.isPaused = !this.isPaused;
      if (this.isPaused) {
        this.stopGame();
      } else {
        this.startGame();
      }
    },
    rotate() {
      if (!this.isPaused && this.gameInterval) {
        // 旋转逻辑实现
        console.log('旋转方块');
        // 实际游戏中需要添加方块旋转的具体实现
      }
    },
    moveLeft() {
      if (!this.isPaused && this.gameInterval) {
        // 左移逻辑实现
        console.log('左移方块');
        // 实际游戏中需要添加方块左移的具体实现
      }
    },
    moveRight() {
      if (!this.isPaused && this.gameInterval) {
        // 右移逻辑实现
        console.log('右移方块');
        // 实际游戏中需要添加方块右移的具体实现
      }
    },
    moveDown() {
      if (!this.isPaused && this.gameInterval) {
        // 下移逻辑实现
        console.log('下移方块');
        // 实际游戏中需要添加方块下移的具体实现
        // 这里只是简单模拟分数增加
        this.score += 10;
      }
    },
    handleTouchStart(e) {
      // 触摸开始处理
    },
    handleTouchMove(e) {
      // 触摸移动处理
    },
    handleTouchEnd(e) {
      // 触摸结束处理
    },
    navigateBack() {
      uni.switchTab({url: '/pages/index/index'});
    }
  }
};
</script>

<style scoped>
.tetris-page {
  background-color: #fff;
  min-height: 100vh;
  padding: 20rpx;
}
.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.game-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}
.back-btn {
  background-color: #ff99cc;
  color: white;
  border-radius: 50rpx;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
}
.game-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.game-canvas {
  width: 600rpx;
  height: 1200rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 0 10rpx rgba(0,0,0,0.1);
}
.game-info {
  margin-top: 20rpx;
  width: 600rpx;
}
.score-panel {
  text-align: center;
  margin-bottom: 20rpx;
}
.info-label {
  font-size: 28rpx;
  color: #666;
}
.score-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6699;
}
.controls {
  margin-top: 20rpx;
  width: 600rpx;
}
.control-row {
  display: flex;
  justify-content: center;
  gap: 15rpx;
  margin-bottom: 15rpx;
}
.game-btn {
  background-color: #ff69b4;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  margin-top: 15rpx;
  width: 100%;
}
.control-btn {
  background-color: #ff69b4;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 15rpx 20rpx;
  font-size: 28rpx;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}
.control-btn:active {
  background-color: #ff85c0;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
</style>