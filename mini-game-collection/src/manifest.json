{
    "name" : "hello uni-app",
    "appid" : "__UNI__HelloUniApp",
    "description" : "应用描述",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "nvueLaunchMode" : "fast",
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "OAuth" : {},
            "Payment" : {},
            "Push" : {},
            "Share" : {},
            "Speech" : {},
            "VideoPlayer" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_MOCK_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.GET_TASKS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.SEND_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_USER_PRESENT\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {
                "UIBackgroundModes" : [ "audio" ],
                "urlschemewhitelist" : [ "baidumap", "iosamap" ]
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "speech" : {
                    "ifly" : {}
                }
            },
            "orientation" : [ "portrait-primary" ]
        },
        "uniStatistics" : {
            "enable" : true
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    "quickapp-native" : {
        "icon" : "/static/logo.png",
        "package" : "com.example.demo",
        "features" : [
            {
                "name" : "system.clipboard"
            }
        ]
    },
    "quickapp-webview" : {
        "icon" : "/static/logo.png",
        "package" : "com.example.demo",
        "minPlatformVersion" : 1070,
        "versionName" : "1.0.0",
        "versionCode" : 100
    },
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "演示定位能力"
            }
        },
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        },
        "dynamicLib" : {
            "editorLib" : {
                "provider" : "swan-editor"
            }
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-jd" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "h5" : {
        "template" : "template.h5.html",
        "router" : {
            "mode" : "history",
            "base" : ""
        },
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "TKUBZ-D24AF-GJ4JY-JDVM2-IBYKK-KEBCU"
                }
            }
        },
        "async" : {
            "timeout" : 20000
        },
        "uniStatistics" : {
            "enable" : true
        }
    },
    "vueVersion" : "3",
    "mp-kuaishou" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-lark" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp-webview-huawei" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp-webview-union" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "uniStatistics" : {
        "version" : "2",
        "enable" : true
    }
}
