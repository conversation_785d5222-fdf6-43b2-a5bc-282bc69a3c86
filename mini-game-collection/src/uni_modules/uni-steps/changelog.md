## 1.1.2（2024-03-28）
- 修复 uni-steps为竖排列时，文本长度过长引起点错乱的bug
## 1.1.1（2021-11-22）
- 修复 vue3中某些scss变量无法找到的问题
## 1.1.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-steps](https://uniapp.dcloud.io/component/uniui/uni-steps)
## 1.0.8（2021-05-12）
- 新增 项目示例地址
## 1.0.7（2021-05-06）
- 修复 uni-steps 横向布局时，多行文字高度不合理的 bug
## 1.0.6（2021-04-21）
- 优化 添加依赖 uni-icons, 导入后自动下载依赖
## 1.0.5（2021-02-05）
- 优化 组件引用关系，通过uni_modules引用组件

## 1.0.4（2021-02-05）
- 调整为uni_modules目录规范
