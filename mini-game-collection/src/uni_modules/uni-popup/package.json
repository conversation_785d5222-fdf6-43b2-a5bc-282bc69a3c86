{"id": "uni-popup", "displayName": "uni-popup 弹出层", "version": "1.9.6", "description": " Popup 组件，提供常用的弹层", "keywords": ["uni-ui", "弹出层", "弹窗", "popup", "弹框"], "repository": "https://github.com/dcloudio/uni-ui", "engines": {"HBuilderX": ""}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/@dcloudio/uni-ui", "type": "component-vue"}, "uni_modules": {"dependencies": ["uni-scss", "uni-transition"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"App": {"app-vue": "y", "app-nvue": "y", "app-harmony": "u", "app-uvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}