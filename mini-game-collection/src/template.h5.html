<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
		<title>
			<%= htmlWebpackPlugin.options.title %>
		</title>
		<!-- 正式发布的时候使用，开发期间不启用。↓ -->
        <!-- <script src="/h5/touch-emulator.js"></script>
		<script>
            TouchEmulator();
			if (document.documentElement.clientWidth > 1024) {
				window.location.href = '/h5/pcguide.html#'+location.pathname+location.search;
			}
		</script>
        <style>
            ::-webkit-scrollbar{
                display: none;
            }
        </style>
        <script>
            var _hmt = _hmt || [];
            (function() {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?";// 百度统计key
                var s = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(hm, s);
            })();
        </script> -->
        <!-- 正式发布的时候使用，开发期间不启用。↑ -->
		<script>
			// document.addEventListener('DOMContentLoaded', function() {
			// 	document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
			// })
		</script>
		<script src="<%= BASE_URL %>static/web/image-resize-3.0.1.min.js"></script>
		<script src="<%= BASE_URL %>static/web/quill-1.3.7.min.js"></script>
		<link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
	</head>
	<body>
		<!-- 该文件为 H5 平台的模板 HTML，并非应用入口。 -->
		<!-- 请勿在此文件编写页面代码或直接运行此文件。 -->
		<!-- 详见文档：https://uniapp.dcloud.io/collocation/manifest?id=h5-template -->
		<noscript>
			<strong>Please enable JavaScript to continue.</strong>
		</noscript>
		<div id="app"></div>
		<!-- built files will be auto injected -->
		<script>
			/*BAIDU_STAT*/
		</script>
	</body>
</html>
